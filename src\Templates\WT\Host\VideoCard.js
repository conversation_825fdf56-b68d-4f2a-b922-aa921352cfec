import React from 'react';
import {connect} from "react-redux";
import Wave from '../../../Tools/ToolComponents/AudioVis';
import * as HostActions from "../../../Actions/HostAction"
import '../../../styles/avatar.css';
class VideoCard extends React.Component {
  constructor(props){
    super(props);
    this.ref= React.createRef();
    this.state = {
      isSpeaking: false,
      audioLevel: 0,
      speakingThreshold: 10, // Threshold to detect speaking
      speakingTimeout: null
    };
  }

  // Get user initials for avatar
  getInitials = (name) => {
    if (!name) return '?';

    // Remove the (Host) part if present
    const cleanName = name.replace(/\s*\(Host\)\s*$/i, '');

    const names = cleanName.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    } else {
      return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
    }
  }

  // Generate consistent color based on name
  getColorFromName = (name) => {
    if (!name) return '3b82f6'; // Default blue

    // For host, use the stored real name to ensure consistency
    let nameToUse = name;
    if (name.includes('(Host)')) {
      // Use the stored host name if available
      const storedHostName = localStorage.getItem('hostRealName');
      if (storedHostName) {
        nameToUse = storedHostName;
      } else {
        // Remove the (Host) part if stored name not available
        nameToUse = name.replace(/\s*\(Host\)\s*$/i, '');
      }
    }

    // Simple hash function to generate a consistent color for the same name
    let hash = 0;
    for (let i = 0; i < nameToUse.length; i++) {
      hash = nameToUse.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convert to hex color
    let color = Math.abs(hash).toString(16);
    // Pad with zeros if needed
    while (color.length < 6) {
      color = '0' + color;
    }
    // Take the last 6 characters to ensure valid hex color
    return color.substring(0, 6);
  }
  componentDidMount()
{
  // Initialize userVideoAudio state if not present (for new/rejoining peers)
  if (!this.props.userVideoAudio[this.props.peer.peerID]) {
    console.log('Host VideoCard: Initializing userVideoAudio for peer:', this.props.peer.peerID,
                'with initial video state:', this.props.peer.video);
    this.props.SetUserAV(this.props.peer.peerID, {
      video: this.props.peer.video !== undefined ? this.props.peer.video : true,
      audio: this.props.peer.audio !== undefined ? this.props.peer.audio : true,
      screen: false
    });
  }

  let wave = new Wave();
  const peer = this.props.peer;
  let audioContext;
  let analyser;
  let dataArray;
  let audioSource;

  peer.on('stream', (stream) => {
    this.props.ConnectMUX(stream);
    this.ref.current.srcObject = stream;

    // Initialize audio visualization
    wave.fromStream(stream, peer.peerID, {
      colors: ['#ffffff00', '#2979FF'],
    });

    // Set up audio analysis for speaking detection
    if (stream.getAudioTracks().length > 0) {
      try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioContext.createAnalyser();
        analyser.fftSize = 32;
        dataArray = new Uint8Array(analyser.frequencyBinCount);
        audioSource = audioContext.createMediaStreamSource(stream);
        audioSource.connect(analyser);

        // Check audio levels periodically
        this.audioLevelInterval = setInterval(() => {
          if (analyser && this.props.userVideoAudio[this.props.peer.peerID]?.audio) {
            analyser.getByteFrequencyData(dataArray);

            // Calculate average audio level
            const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;

            // Update state based on audio level
            if (average > this.state.speakingThreshold) {
              if (!this.state.isSpeaking) {
                this.setState({ isSpeaking: true, audioLevel: average });
              }

              // Clear any existing timeout
              if (this.state.speakingTimeout) {
                clearTimeout(this.state.speakingTimeout);
              }

              // Set timeout to stop speaking state after silence
              const timeout = setTimeout(() => {
                this.setState({ isSpeaking: false });
              }, 500);

              this.setState({ speakingTimeout: timeout });
            }
          }
        }, 100);
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
      }
    }
  });

  peer.on('track', () => {});
}

componentWillUnmount() {
  // Clear intervals and timeouts
  if (this.audioLevelInterval) {
    clearInterval(this.audioLevelInterval);
  }

  if (this.state.speakingTimeout) {
    clearTimeout(this.state.speakingTimeout);
  }
}
shouldComponentUpdate(){
  return true
}
// let wave = new Wave();
  render(){
    // Check if this is screen sharing and if we're in a default/no project session
    const isScreenSharing = this.props.userVideoAudio[this.props.peer.peerID] &&
                            this.props.userVideoAudio[this.props.peer.peerID].screen;

    // For screen sharing in participants tab, don't use the fullscreen ScreenSharing class
    // Instead, keep it contained within the participants area
    const videoClass = isScreenSharing ? "fixed-video" : "fixed-video";

    // Check if both screen sharing and project content are active
    const hasProject = this.props.SessionDetails && this.props.SessionDetails.project_id && this.props.SessionDetails.type !== 'default';
    const showPinIcon = isScreenSharing && hasProject;

    return (
      <>
      <div className={" custom-aspect-ratio aspect-video " + videoClass} >

    <div className="VideoControls_Div" >
          <div className="VideoOffname" style={{marginRight:"8px",textAlign:"start"}}><div style={{position:"absolute",width:"100%",bottom:"0px",padding:"0 0 0 4px"}}> {this.props.peer.userName}</div></div>

        </div>
        {this.props.userVideoAudio[this.props.peer.peerID] && this.props.userVideoAudio[this.props.peer.peerID].audio ?
        <button onClick={()=>{this.props.MuteGuest(this.props.peer.extra.id,this.props.userVideoAudio[this.props.peer.peerID].audio)}} className="right-1 bottom-1 p-1 absolute z-10 bg-[black]/20 bg-opacity-20 rounded-full text-white"><svg  className="w-5 h-5" viewBox="0 0 24 24">
        <path fill="#fff" fillRule="evenodd" d="M13 17.92V20h2.105c.493 0 .895.402.895.895v.21c0 .493-.402.895-.895.895h-6.21C8.402 22 8 21.598 8 21.106v-.211c0-.493.402-.895.895-.895H11v-2.08c-3.387-.488-6-3.4-6-6.92 0-.552.447-1 1-1 .553 0 1 .448 1 1 0 2.757 2.243 5 5 5s5-2.243 5-5c0-.552.447-1 1-1 .553 0 1 .448 1 1 0 3.52-2.613 6.432-6 6.92zM10 6c0-1.103.897-2 2-2s2 .897 2 2v5c0 1.103-.897 2-2 2s-2-.897-2-2V6zm2 9c2.206 0 4-1.794 4-4V6c0-2.205-1.794-4-4-4S8 3.795 8 6v5c0 2.206 1.794 4 4 4z" />
      </svg></button>
      : this.props.userVideoAudio[this.props.peer.peerID] ? <button onClick={()=>{this.props.MuteGuest(this.props.peer.extra.id,this.props.userVideoAudio[this.props.peer.peerID].audio)}} className="right-1 bottom-1 p-1 absolute z-10 bg-[black]/20 bg-opacity-20 rounded-full text-white"><svg className="w-5 h-5"  viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                  <g data-name="mic-off">
                  <rect width="24" height="24" opacity="0"/>
                  <path fill="#fff" d="M10 6a2 2 0 0 1 4 0v5a1 1 0 0 1 0 .16l1.6 1.59A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75L10 7.17z"/>
                  <path fill="#fff" d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z"/>
                  <path fill="#fff" d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z"/>
                  <path fill="#fff" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/>
                  <path fill="#fff" d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z"/>
                  </g>
                  </g>
                  </svg></button> : ""}
                  <div >
        <video
    className="user-video w-full h-full object-cover bg-black"
    id={this.props.peer.peerID+"VIDEO"}
      playsInline
      autoPlay
      ref={this.ref}
    ></video>

    {/* Avatar with first letter when video is off */}
    {/* Show avatar only when we're certain video is off */}
    {(() => {
      const peerVideoState = this.props.userVideoAudio[this.props.peer.peerID];
      const hasVideoStream = this.ref.current && this.ref.current.srcObject &&
                           this.ref.current.srcObject.getVideoTracks().length > 0;

      // If we have userVideoAudio state, use it to determine avatar visibility
      if (peerVideoState) {
        return !peerVideoState.video;
      }

      // If no userVideoAudio state yet, check peer's initial video state
      // This handles the case when new participants join and see existing participants
      if (this.props.peer.video !== undefined) {
        return !this.props.peer.video;
      }

      // As final fallback, check if we have an active video stream
      // Only show avatar if we definitely don't have a video stream
      return !hasVideoStream;
    })() && (
      <div
        className="absolute inset-0 flex items-center justify-center"
        style={{
          background: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), #${this.getColorFromName(this.props.peer.userName)}`
        }}
      >
        <div
          className="avatar-bg"
          style={{
            background: `radial-gradient(circle, #${this.getColorFromName(this.props.peer.userName)}30 0%, rgba(0,0,0,0) 70%)`
          }}
        ></div>
        <div
          className={`relative w-20 h-20 rounded-full flex items-center justify-center avatar-circle ${this.state.isSpeaking && this.props.userVideoAudio[this.props.peer.peerID]?.audio ? 'avatar-speaking' : ''}`}
          style={{
            backgroundColor: `#${this.getColorFromName(this.props.peer.userName)}`,
            boxShadow: `0 0 30px #${this.getColorFromName(this.props.peer.userName)}80`
          }}
        >
          <span className="text-white text-2xl font-bold z-10 relative">
            {this.getInitials(this.props.peer.userName)}
          </span>
        </div>
      </div>
    )}

        {/* Pin icon - show when screen sharing is active and both project and screen sharing are available */}
        {showPinIcon && (
          <button
            onClick={() => this.props.onTogglePin && this.props.onTogglePin()}
            className="absolute top-2 right-2 z-10 bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-lg transition-colors"
            title="Pin screen sharing to main area"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
            </svg>
          </button>
        )}

        {/* Screen sharing indicator text */}
        {isScreenSharing && (
          <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
            {this.props.peer.extra && this.props.peer.extra.type === 'host' ? 'Host is presenting' : `${this.props.peer.userName} is presenting`}
          </div>
        )}

    </div>
    </div>
    </>
    );
  }


};



const mapStateTothisprops = state => {
  return {
    userVideoAudio:state.Call.userVideoAudio,
  }
}
const mapActionstothisprops={...HostActions}


export default connect(mapStateTothisprops, mapActionstothisprops)(VideoCard)
